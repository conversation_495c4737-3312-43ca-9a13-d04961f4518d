import { createState } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { Dialogue } from '../../../api/game/dialogue'
import { Equipment } from '../../../api/game/equipment'
import { GameObjects } from '../../../api/game/gameObjects'
import { GameTab } from '../../../api/game/gameTab'
import { Inventory } from '../../../api/game/inventory'
import { Npcs } from '../../../api/game/npcs'
import { Walking, WalkUtils } from '../../../api/game/walking'
import { Widgets } from '../../../api/game/widgets'
import { WorldHopping } from '../../../api/game/worldHopping'
import { MenuOpcode } from '../../../api/model/menuOpcode'
import { Tile } from '../../../api/model/tile'
import { createGeState } from '../../../api/script-utils/states/geStates'
import { PaintTimer } from '../../../api/utils/paintTimer'
import { Time } from '../../../api/utils/time'
import { log } from '../../../api/utils/utils'
import { Player } from '../../../api/wrappers/player'
import { ItemId } from '../../../data/itemId'
import { Locations } from '../../../data/locations'
import { BfData } from '../bfConstants'
import { BlastFurnace } from '../blastFurnace'
import { BfBarState } from './bfBarState'



export class MakeIronBarStrategy extends BfBarState {
    private foremanTimer: PaintTimer = new PaintTimer()
    public lastStateChangeTimer: PaintTimer = new PaintTimer()



    onBackgroundAction(): void {
        Walking.setRunAuto()
        GameTab.inventory.open()

        if (Dialogue.contains('You must ask the foreman')) {
            this.setState(this.payForemanState)
            return
        }


    }

    onAction(): void {
        if (!WorldHopping.switchToWorld(358)) {
            return
        }

        if (!this.walkBlastFurnace()) {
            return
        }

        if (Dialogue.containsOption("Yes, and don't ask again")) {
            Dialogue.goNext("Yes, and don't ask again")
            Time.sleep(600, 1200)
            return
        }

        if (!this.ensureCoffer()) {
            return
        }

        if (!Equipment.withdrawAndEquip(1580)) {
            return
        }

        this.setState(this.collectIronBars)

    }


    payForeman() {
        if (Locations.bankBlastFurnace.distance() > 35) {
            this.setState(this.withdrawIronOre)
            return
        }

        const npc = Npcs.getById(this.FOREMAN)

        //TODO Bank not found ex handler
        if (!Withdraw.builder().id(995).amount(2500).minimumAmount(2500).ensureSpace().withdraw()) return

        if (npc) {
            npc.click(MenuOpcode.NPC_THIRD_OPTION)
            Time.sleep(() => Dialogue.containsOption('Yes'))
        }

        if (Dialogue.isOpen()) {
            if (Dialogue.containsOption('Yes')) {
                Dialogue.goNext('Yes')
                Time.sleep(() => Dialogue.contains('Okay'))
            }
            if (Dialogue.contains('Okay')) {
                this.foremanTimer.reset()
                this.setState(this.withdrawIronOre)
            } else {
                if (Time.sleep(() => Dialogue.contains('Okay'))) {
                    this.foremanTimer.reset()
                    this.setState(this.withdrawIronOre)
                }
            }
        }
    }

    payForemanState = createState("Paying foreman", () => {
        this.payForeman()
    })

    withdrawIronOre = createState("Withdrawing iron ore", () => {
        if (!Withdraw.all(
            BlastFurnace.ge,
            Withdraw.id(ItemId.IRON_ORE, 28).minimumAmount(1).ensureSpace()
        )) {
            return
        }

        this.setState(this.depositIronOre)
    })

    depositIronOre = createState("Depositing iron ore", () => {
        if (Inventory.get().contains(ItemId.IRON_ORE)) {
            this.putOre()
            return
        }
        this.setState(this.collectIronBars)
    })

    collectIronBars = createState("Collecting iron bars", () => {
        if (!Walking.walkTo(new Tile(1940, 4962, 0), 0)) {
            return
        }

        if (!Time.sleep(5000, () => BfData.ironBars > 0)) {
            this.setState(this.withdrawIronOre)
            return
        }

        if (Widgets.get(17694734) != null) {
            Widgets.get(17694734).click(57, 1)
            if (Time.sleep(2000, 30, () => Inventory.get().contains(ItemId.IRON_BAR))) {
                 this.setState(this.withdrawIronOre)
            }
            return
        }

        GameObjects.getById(this.BAR_DISPENDER).click(MenuOpcode.GAME_OBJECT_FIRST_OPTION)
        if (Time.sleep(1200, 10, () => Widgets.get(17694734) != null)) {
            Widgets.get(17694734).click(57, 1)

           if (Time.sleep(2000, 30, () => Inventory.get().contains(ItemId.IRON_BAR))) {
                this.setState(this.withdrawIronOre)
            }
            return
        }


    })

    
}


